<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { checkinApi } from '@/api/checkin'

const authStore = useAuthStore()

// 签到状态
const checkinStatus = ref({
  todayChecked: false,
  consecutiveDays: 0,
  totalDays: 0,
  lastCheckinDate: ''
})

// 签到记录
const checkinHistory = ref<Array<{
  id: number
  date: string
  reward: number
  consecutive: number
}>>([])

// 签到日历
const calendarData = ref<Array<{
  date: string
  checked: boolean
  reward?: number
}>>([])

const loading = ref(false)
const checkinLoading = ref(false)

onMounted(async () => {
  await loadCheckinData()
})

// 加载签到数据
const loadCheckinData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取签到数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    // 模拟数据
    checkinStatus.value = {
      todayChecked: false,
      consecutiveDays: 5,
      totalDays: 28,
      lastCheckinDate: '2024-01-15'
    }
    
    // 生成签到历史
    checkinHistory.value = Array.from({ length: 10 }, (_, i) => ({
      id: i + 1,
      date: new Date(Date.now() - i * 86400000).toISOString().split('T')[0],
      consecutive: Math.floor(Math.random() * 10) + 1
    }))
    
    // 生成日历数据
    generateCalendarData()
  } catch (error) {
    console.error('Load checkin data error:', error)
    ElMessage.error('加载签到数据失败')
  } finally {
    loading.value = false
  }
}

// 生成日历数据
const generateCalendarData = () => {
  const today = new Date()
  const currentMonth = today.getMonth()
  const currentYear = today.getFullYear()
  
  // 获取当月第一天和最后一天
  const firstDay = new Date(currentYear, currentMonth, 1)
  const lastDay = new Date(currentYear, currentMonth + 1, 0)
  
  // 获取第一天是星期几
  const startWeekday = firstDay.getDay()
  
  const calendar = []
  
  // 添加上个月的日期（填充）
  for (let i = startWeekday - 1; i >= 0; i--) {
    const date = new Date(firstDay.getTime() - (i + 1) * 86400000)
    calendar.push({
      date: date.toISOString().split('T')[0],
      checked: false,
      isCurrentMonth: false
    })
  }
  
  // 添加当月的日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(currentYear, currentMonth, day)
    const dateStr = date.toISOString().split('T')[0]
    
    // 模拟签到状态
    const isChecked = Math.random() > 0.7 && date <= today

    calendar.push({
      date: dateStr,
      checked: isChecked,
      isCurrentMonth: true,
      isToday: dateStr === today.toISOString().split('T')[0]
    })
  }
  
  calendarData.value = calendar
}

// 执行签到
const doCheckin = async () => {
  if (checkinStatus.value.todayChecked) {
    ElMessage.warning('今天已经签到过了')
    return
  }
  
  checkinLoading.value = true
  try {
    // TODO: 调用签到API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    // 更新签到状态
    checkinStatus.value.todayChecked = true
    checkinStatus.value.consecutiveDays++
    checkinStatus.value.totalDays++
    checkinStatus.value.lastCheckinDate = new Date().toISOString().split('T')[0]

    // 更新日历
    const today = new Date().toISOString().split('T')[0]
    const todayItem = calendarData.value.find(item => item.date === today)
    if (todayItem) {
      todayItem.checked = true
    }

    // 添加到历史记录
    checkinHistory.value.unshift({
      id: Date.now(),
      date: today,
      consecutive: checkinStatus.value.consecutiveDays
    })

    ElMessage.success('签到成功！')
    
    // 刷新用户信息
    await authStore.fetchUserInfo()
  } catch (error) {
    console.error('Checkin error:', error)
    ElMessage.error('签到失败，请重试')
  } finally {
    checkinLoading.value = false
  }
}



// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 获取星期几
const getWeekdays = () => {
  return ['日', '一', '二', '三', '四', '五', '六']
}
</script>

<template>
  <div class="space-y-6">
    <!-- 签到状态卡片 -->
    <div class="card-base p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            每日签到
          </h2>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            坚持签到，获得更多邮箱配额
          </p>
        </div>
        <el-button 
          type="primary" 
          size="large"
          @click="doCheckin"
          :loading="checkinLoading"
          :disabled="checkinStatus.todayChecked"
        >
          <font-awesome-icon 
            :icon="['fas', checkinStatus.todayChecked ? 'check' : 'calendar-plus']" 
            class="mr-2"
          />
          {{ checkinStatus.todayChecked ? '今日已签到' : '立即签到' }}
        </el-button>
      </div>

      <!-- 签到统计 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <div class="flex items-center space-x-3">
            <font-awesome-icon 
              :icon="['fas', 'fire']" 
              class="text-blue-600 dark:text-blue-400 text-2xl"
            />
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">连续签到</p>
              <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {{ checkinStatus.consecutiveDays }} 天
              </p>
            </div>
          </div>
        </div>

        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
          <div class="flex items-center space-x-3">
            <font-awesome-icon 
              :icon="['fas', 'calendar-check']" 
              class="text-green-600 dark:text-green-400 text-2xl"
            />
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">累计签到</p>
              <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                {{ checkinStatus.totalDays }} 天
              </p>
            </div>
          </div>
        </div>


      </div>


    </div>

    <!-- 签到日历 -->
    <div class="card-base p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
        签到日历
      </h3>

      <div class="max-w-md mx-auto">
        <!-- 星期标题 -->
        <div class="grid grid-cols-7 gap-2 mb-2">
          <div
            v-for="weekday in getWeekdays()"
            :key="weekday"
            class="text-center text-sm font-medium text-gray-600 dark:text-gray-400 py-2"
          >
            {{ weekday }}
          </div>
        </div>

        <!-- 日历格子 -->
        <div class="grid grid-cols-7 gap-2">
          <div
            v-for="day in calendarData"
            :key="day.date"
            :class="[
              'aspect-square flex items-center justify-center text-sm rounded-lg relative',
              day.isCurrentMonth 
                ? day.isToday
                  ? 'bg-blue-500 text-white font-bold'
                  : day.checked
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-400'
            ]"
          >
            {{ new Date(day.date).getDate() }}
          </div>
        </div>

        <!-- 图例 -->
        <div class="flex items-center justify-center space-x-4 mt-4 text-xs">
          <div class="flex items-center space-x-1">
            <div class="w-3 h-3 bg-green-100 dark:bg-green-900/30 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">已签到</span>
          </div>
          <div class="flex items-center space-x-1">
            <div class="w-3 h-3 bg-blue-500 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">今天</span>
          </div>
          <div class="flex items-center space-x-1">
            <div class="w-3 h-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">未签到</span>
          </div>
        </div>
      </div>
    </div>



    <!-- 签到历史 -->
    <div class="card-base p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
        签到历史
      </h3>

      <div class="space-y-3">
        <div
          v-for="record in checkinHistory.slice(0, 10)"
          :key="record.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <font-awesome-icon 
                :icon="['fas', 'check']" 
                class="text-green-600 dark:text-green-400 text-sm"
              />
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-gray-100">
                {{ formatDate(record.date) }} 签到
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                连续 {{ record.consecutive }} 天
              </p>
            </div>
          </div>

          <div class="text-right">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ formatDate(record.date) }}
            </p>
            <el-tag type="success" size="small">已签到</el-tag>
          </div>
        </div>
      </div>

      <div class="flex justify-center mt-4">
        <el-button size="small">
          <font-awesome-icon :icon="['fas', 'history']" class="mr-1" />
          查看更多记录
        </el-button>
      </div>
    </div>
  </div>
</template>
